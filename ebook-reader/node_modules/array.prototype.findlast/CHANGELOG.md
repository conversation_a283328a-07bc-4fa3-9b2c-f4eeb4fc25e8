# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.5](https://github.com/es-shims/Array.prototype.findLast/compare/v1.2.4...v1.2.5) - 2024-03-19

### Commits

- [meta] remove useless ESM [`30b3843`](https://github.com/es-shims/Array.prototype.findLast/commit/30b38436f06003ab2ea0a9571e3bc5bbf6d6e9fb)
- [Deps] update `call-bind`, `es-abstract` [`f339e4d`](https://github.com/es-shims/Array.prototype.findLast/commit/f339e4d0748f32a640e58ce3282d98d1baf73269)
- [actions] remove redundant finisher [`3b66016`](https://github.com/es-shims/Array.prototype.findLast/commit/3b66016b8f9e19084cdcafa1a125eb6b2d26f527)
- [Refactor] use `es-object-atoms` where possible [`c7146f1`](https://github.com/es-shims/Array.prototype.findLast/commit/c7146f172d379e423abb95928ca29659d710dceb)
- [Dev Deps] update `hasown`, `tape` [`16149a0`](https://github.com/es-shims/Array.prototype.findLast/commit/16149a0cdf6e8a78e386e435d8a6b1f83a792142)

## [v1.2.4](https://github.com/es-shims/Array.prototype.findLast/compare/v1.2.3...v1.2.4) - 2024-02-05

### Commits

- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `es-shim-unscopables`, `get-intrinsic` [`6960289`](https://github.com/es-shims/Array.prototype.findLast/commit/6960289aad2898b3c1c3b874937fe171ccd195cb)
- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`96e976a`](https://github.com/es-shims/Array.prototype.findLast/commit/96e976af6cbe6240472bf6ac509526e66bb48fbc)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`e41c671`](https://github.com/es-shims/Array.prototype.findLast/commit/e41c6718bb4e1f498bc0c7d850fe6f595e6314d1)
- [Dev Deps] use `hasown` instead of `has` [`3e81897`](https://github.com/es-shims/Array.prototype.findLast/commit/3e818971d0432e11e639d4e0f13fba7ca7f1392d)
- [Dev Deps] update `object-inspect`, `tape` [`09387d9`](https://github.com/es-shims/Array.prototype.findLast/commit/09387d910af91741959ee9106f20b8379b80fdcb)

## [v1.2.3](https://github.com/es-shims/Array.prototype.findLast/compare/v1.2.2...v1.2.3) - 2023-08-29

### Commits

- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`ed6e699`](https://github.com/es-shims/Array.prototype.findLast/commit/ed6e69963e3b7252ce6fc82a92f0843bf92e2cb8)
- [Tests] add passing test262 test [`ba57e7f`](https://github.com/es-shims/Array.prototype.findLast/commit/ba57e7fb3d85c5d022f84538ac610cbbad3a1f06)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`fdf9269`](https://github.com/es-shims/Array.prototype.findLast/commit/fdf9269de92629dbbb101e91971efbab45634020)
- [Tests] use `globalthis` [`d56fd28`](https://github.com/es-shims/Array.prototype.findLast/commit/d56fd280792c2fa78d6b4efd9c419df5bf49e6d8)

## [v1.2.2](https://github.com/es-shims/Array.prototype.findLast/compare/v1.2.1...v1.2.2) - 2022-11-02

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`c8bd8e9`](https://github.com/es-shims/Array.prototype.findLast/commit/c8bd8e9410e3b0468754bcc722d10eade90d6b8d)
- [Deps] update `es-abstract`, `get-intrinsic` [`7dd1e37`](https://github.com/es-shims/Array.prototype.findLast/commit/7dd1e3706aa4d1dc8007dc430ad721b787cceece)
- [actions] update rebase action to use reusable workflow [`116bfa3`](https://github.com/es-shims/Array.prototype.findLast/commit/116bfa3374f7adbe4363ee186ae397d6175a1386)
- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`8a6fcb5`](https://github.com/es-shims/Array.prototype.findLast/commit/8a6fcb543f3854f4124b26a60d651f26cb836658)
- [Dev Deps] update `es-value-fixtures`, `object-inspect`, `tape` [`899e29a`](https://github.com/es-shims/Array.prototype.findLast/commit/899e29acdeb046b92e6a19739cc97f0605bd074d)
- [Dev Deps] update `aud`, `tape` [`2019883`](https://github.com/es-shims/Array.prototype.findLast/commit/2019883d4a054b95856b6d421e106b9a0fe0f20b)
- [Tests] add indication of whether it was shimmed [`c940767`](https://github.com/es-shims/Array.prototype.findLast/commit/c9407677645d551481e8b98f96297fec7518165a)

## [v1.2.1](https://github.com/es-shims/Array.prototype.findLast/compare/v1.2.0...v1.2.1) - 2022-04-11

### Commits

- [Refactor] use `es-shim-unscopables` [`e85dfc7`](https://github.com/es-shims/Array.prototype.findLast/commit/e85dfc7f324f61e1dc2c99129f4bcc7837ce3ac4)

## [v1.2.0](https://github.com/es-shims/Array.prototype.findLast/compare/v1.1.1...v1.2.0) - 2022-04-11

### Commits

- [actions] reuse common workflows [`47e3be7`](https://github.com/es-shims/Array.prototype.findLast/commit/47e3be7cda282144ddc18204534ec5983055a48e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`da9ec92`](https://github.com/es-shims/Array.prototype.findLast/commit/da9ec926ee4393d8cf2e2b74202a059a92ef59ac)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`2658604`](https://github.com/es-shims/Array.prototype.findLast/commit/26586043f36390c4967972c2caf318c0bbf583fb)
- [actions] update codecov uploader [`9e4ac5e`](https://github.com/es-shims/Array.prototype.findLast/commit/9e4ac5e104a2f258cb818a19d3e6eb4023d1edd3)
- [New] `shim`/`auto`: add `findLast` to `Symbol.unscopables` [`a3703b0`](https://github.com/es-shims/Array.prototype.findLast/commit/a3703b0dbeefb1dcd4c4fcec9f89c67bc7513821)
- [meta] use `prepublishOnly` script for npm 7+ [`499fd61`](https://github.com/es-shims/Array.prototype.findLast/commit/499fd61f61d094d997b53c34628e477089b6ff96)
- [Deps] update `es-abstract` [`9833a38`](https://github.com/es-shims/Array.prototype.findLast/commit/9833a38b6f6d949636fca1e945b2eee1812e142b)
- [Deps] update `es-abstract` [`3055ac5`](https://github.com/es-shims/Array.prototype.findLast/commit/3055ac5ad56eb224f72c4cc18a06d0af06c6d2a1)
- [Dev Deps] update `@es-shims/api` [`4639a6b`](https://github.com/es-shims/Array.prototype.findLast/commit/4639a6b10b3ecb84e1bc3e645cad1c60de8d99da)

## [v1.1.1](https://github.com/es-shims/Array.prototype.findLast/compare/v1.1.0...v1.1.1) - 2021-10-01

### Commits

- [Refactor] remove ESM entry points [`10118cc`](https://github.com/es-shims/Array.prototype.findLast/commit/10118cc9b896f4c7fa01bfbab17018400b2a7ddb)
- [Tests] add new tests from test262 [`11a6b0a`](https://github.com/es-shims/Array.prototype.findLast/commit/11a6b0a9d34bc6bbd8271b37594ec9ba5fd4ca87)
- [Deps] update `es-abstract` [`ad74212`](https://github.com/es-shims/Array.prototype.findLast/commit/ad7421298490b3355b1d3f0a8fa51b8704e5a0ba)
- [readme] fix URLs [`ea7f11e`](https://github.com/es-shims/Array.prototype.findLast/commit/ea7f11e9ee4961c68104ba43958f5a45a0cffa65)
- [Dev Deps] update `@ljharb/eslint-config`, `@es-shims/api` [`b834c11`](https://github.com/es-shims/Array.prototype.findLast/commit/b834c114ae5bea9c1002a31574545b01c479c2fd)

## [v1.1.0](https://github.com/es-shims/Array.prototype.findLast/compare/v1.0.0...v1.1.0) - 2021-08-07

### Commits

- [New] add ESM entry points [`4830a17`](https://github.com/es-shims/Array.prototype.findLast/commit/4830a173290c5b6fbfda494936ebfc13fb188e9b)
- [Fix] ES3 engines: ensure nullish receiver throws [`3483d48`](https://github.com/es-shims/Array.prototype.findLast/commit/3483d489b0a2c17509d34aa386de8b83d4c6c0a1)
- [Fix] add missing entry points to `exports` [`db8fe56`](https://github.com/es-shims/Array.prototype.findLast/commit/db8fe56e3196ea8e74b6c4bda67efef03f6c6594)
- [Dev Deps] update `eslint`, `tape` [`a59f069`](https://github.com/es-shims/Array.prototype.findLast/commit/a59f069678049d3d8589c2ea22cf65160998fde3)
- [Deps] update `es-abstract` [`4faa274`](https://github.com/es-shims/Array.prototype.findLast/commit/4faa27421b982962f4b68e199308bfc25300ccc4)

## v1.0.0 - 2021-07-13

### Commits

- Implementation & Tests [`70cf3b1`](https://github.com/es-shims/Array.prototype.findLast/commit/70cf3b1254a43d7fc32bc7726f6c372656f84182)
- Initial commit [`d94083f`](https://github.com/es-shims/Array.prototype.findLast/commit/d94083fc3de59677540a3021c6f3082a22a804e6)
- npm init [`e016a98`](https://github.com/es-shims/Array.prototype.findLast/commit/e016a98c2ee504e43a7d6018fae31cbe972c1db9)
- Only apps should have lockfiles [`cb11c67`](https://github.com/es-shims/Array.prototype.findLast/commit/cb11c670b831ede1df53cf312dc6b0bcd96236cc)
