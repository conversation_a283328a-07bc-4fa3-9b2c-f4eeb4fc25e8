@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap');

:root {
  --foreground: #1f2937;
  --background: #ffffff;
  --card-background: #f9fafb;
  --border: #e5e7eb;
  --primary: #0ea5e9;
  --secondary: #d946ef;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground: #f9fafb;
    --background: #111827;
    --card-background: #1f2937;
    --border: #374151;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
}

/* Reading styles */
.reading-content {
  font-family: 'Crimson Text', Georgia, serif;
  font-size: 1.125rem;
  line-height: 1.8;
  max-width: 65ch;
  margin: 0 auto;
}

.reading-content p {
  margin-bottom: 1.5rem;
  text-align: justify;
}

.reading-content h1,
.reading-content h2,
.reading-content h3 {
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--card-background);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
